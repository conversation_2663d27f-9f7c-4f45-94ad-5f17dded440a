'use client';

import { useState, useEffect } from 'react';
import { EditorialDashboard } from '@/components/admin/EditorialDashboard';
import { SubmissionReviewModal } from '@/components/admin/SubmissionReviewModal';

interface SubmissionItem {
  id: string;
  name: string;
  url: string;
  description: string;
  category: string;
  subcategory?: string;
  submitterName: string;
  submitterEmail: string;
  status: 'pending' | 'under_review' | 'approved' | 'rejected' | 'published';
  submittedAt: string;
  reviewedAt?: string;
  reviewedBy?: string;
  reviewNotes?: string;
  priority: 'high' | 'normal' | 'low';
  pricingType?: string;
}

interface ReviewFormData {
  decision: 'approve' | 'reject' | 'needs_revision';
  reviewNotes: string;
  priority?: 'high' | 'normal' | 'low';
  featuredDate?: string;
  editorialText?: string;
  qualityScore?: number;
}

export default function EditorialDashboardPage() {
  const [selectedSubmission, setSelectedSubmission] = useState<SubmissionItem | null>(null);
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleSubmissionSelect = (submission: SubmissionItem) => {
    setSelectedSubmission(submission);
    setIsReviewModalOpen(true);
  };

  const handleCloseReviewModal = () => {
    setIsReviewModalOpen(false);
    setSelectedSubmission(null);
  };

  const handleSubmitReview = async (submissionId: string, reviewData: ReviewFormData) => {
    try {
      const response = await fetch('/api/admin/editorial/review', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        },
        body: JSON.stringify({
          submissionId,
          decision: reviewData.decision,
          reviewNotes: reviewData.reviewNotes,
          priority: reviewData.priority,
          featuredDate: reviewData.featuredDate,
          editorialText: reviewData.editorialText,
          qualityScore: reviewData.qualityScore,
          reviewerId: 'admin' // In a real app, this would come from the authenticated user
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to submit review');
      }

      const result = await response.json();
      console.log('Review submitted successfully:', result);

      // Trigger refresh of the dashboard data
      setRefreshTrigger(prev => prev + 1);
      
      return result;
    } catch (error) {
      console.error('Failed to submit review:', error);
      throw error;
    }
  };

  return (
    <div className="min-h-screen bg-zinc-900">
      {/* Enhanced Editorial Dashboard with submission selection */}
      <EditorialDashboardWithSelection
        refreshTrigger={refreshTrigger}
      />

      {/* Review Modal */}
      {selectedSubmission && (
        <SubmissionReviewModal
          submission={selectedSubmission}
          isOpen={isReviewModalOpen}
          onClose={handleCloseReviewModal}
          onSubmitReview={handleSubmitReview}
        />
      )}
    </div>
  );
}

// Enhanced Editorial Dashboard component with review functionality
interface EditorialDashboardWithSelectionProps {
  refreshTrigger: number;
}

function EditorialDashboardWithSelection({
  refreshTrigger
}: EditorialDashboardWithSelectionProps) {
  const [submissions, setSubmissions] = useState<SubmissionItem[]>([]);
  const [stats, setStats] = useState({
    totalSubmissions: 0,
    pendingReview: 0,
    underReview: 0,
    approvedToday: 0,
    rejectedToday: 0,
    averageReviewTime: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [processingReview, setProcessingReview] = useState<string | null>(null);
  const [reviewNotes, setReviewNotes] = useState<Record<string, string>>({});

  useEffect(() => {
    loadEditorialData();
  }, [refreshTrigger]);

  const loadEditorialData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch real editorial data
      const [editorialResponse, statsResponse] = await Promise.all([
        fetch('/api/admin/editorial', {
          headers: {
            'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
          }
        }),
        fetch('/api/admin/editorial/stats', {
          headers: {
            'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
          }
        })
      ]);

      if (!editorialResponse.ok) {
        throw new Error('Failed to load editorial data');
      }

      const editorialData = await editorialResponse.json();

      // Transform editorial data to submission format
      const transformedSubmissions: SubmissionItem[] = (editorialData.data || []).map((item: any) => {
        // Handle both editorial reviews and AI generation jobs
        if (item.type === 'editorial_review') {
          return {
            id: item.id,
            name: item.tools?.name || item.toolName || 'Unknown Tool',
            url: item.tools?.website || item.url || '',
            description: item.tools?.description || item.description || '',
            category: item.tools?.category_id || item.category || 'uncategorized',
            submitterName: item.reviewed_by || 'System',
            submitterEmail: `${item.reviewed_by}@admin.local`,
            status: item.review_status === 'pending' ? 'pending' :
                   item.review_status === 'approved' ? 'approved' :
                   item.review_status === 'rejected' ? 'rejected' : 'under_review',
            submittedAt: item.created_at,
            reviewedAt: item.updated_at,
            reviewedBy: item.reviewed_by,
            reviewNotes: item.review_notes,
            priority: item.quality_score > 8 ? 'high' :
                     item.quality_score > 6 ? 'normal' : 'low'
          };
        } else {
          // AI generation job
          return {
            id: `ai_job_${item.id}`,
            name: item.toolName || 'AI Generated Tool',
            url: item.url || '',
            description: item.contentPreview || 'AI generated content pending review',
            category: 'ai-generated',
            submitterName: 'AI System',
            submitterEmail: '<EMAIL>',
            status: 'pending',
            submittedAt: item.generatedAt || item.created_at,
            priority: item.priority || 'normal'
          };
        }
      });

      setSubmissions(transformedSubmissions);

      // Load stats with fallback
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.success ? statsData.stats : calculateStatsFromSubmissions(transformedSubmissions));
      } else {
        setStats(calculateStatsFromSubmissions(transformedSubmissions));
      }

    } catch (err) {
      console.error('Failed to load editorial data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load editorial data');

      // Set empty data on error
      setSubmissions([]);
      setStats({
        totalSubmissions: 0,
        pendingReview: 0,
        underReview: 0,
        approvedToday: 0,
        rejectedToday: 0,
        averageReviewTime: 0
      });
    } finally {
      setLoading(false);
    }
  };

  // Helper function to calculate stats from submissions
  const calculateStatsFromSubmissions = (submissions: SubmissionItem[]) => {
    const today = new Date().toDateString();

    return {
      totalSubmissions: submissions.length,
      pendingReview: submissions.filter(s => s.status === 'pending').length,
      underReview: submissions.filter(s => s.status === 'under_review').length,
      approvedToday: submissions.filter(s =>
        s.status === 'approved' &&
        s.reviewedAt &&
        new Date(s.reviewedAt).toDateString() === today
      ).length,
      rejectedToday: submissions.filter(s =>
        s.status === 'rejected' &&
        s.reviewedAt &&
        new Date(s.reviewedAt).toDateString() === today
      ).length,
      averageReviewTime: 2.5 // Placeholder - would need more complex calculation
    };
  };

  // Handle review actions
  const handleReview = async (id: string, status: 'approved' | 'rejected' | 'needs_revision', notes?: string) => {
    try {
      setProcessingReview(id);
      setError(null);

      // Find the submission to get additional data
      const submission = submissions.find(s => s.id === id);
      if (!submission) {
        throw new Error('Submission not found');
      }

      // Make API call to process review
      const response = await fetch('/api/admin/editorial', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        },
        body: JSON.stringify({
          action: 'review',
          data: {
            id,
            toolId: submission.id.startsWith('ai_job_') ? submission.id.replace('ai_job_', '') : submission.toolId,
            status,
            reviewNotes: notes || '',
            reviewedBy: 'admin',
            qualityScore: submission.qualityScore || 75
          }
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to process review');
      }

      const result = await response.json();
      console.log('Review processed:', result);

      // Update local state
      setSubmissions(prev => prev.map(submission =>
        submission.id === id
          ? {
              ...submission,
              status: status === 'approved' ? 'approved' : status === 'rejected' ? 'rejected' : 'under_review',
              reviewedAt: new Date().toISOString(),
              reviewedBy: 'admin',
              reviewNotes: notes
            }
          : submission
      ));

      // Update stats
      setStats(prev => ({
        ...prev,
        pendingReview: Math.max(0, prev.pendingReview - 1),
        approvedToday: status === 'approved' ? prev.approvedToday + 1 : prev.approvedToday,
        rejectedToday: status === 'rejected' ? prev.rejectedToday + 1 : prev.rejectedToday
      }));

      // Clear review notes for this submission
      setReviewNotes(prev => {
        const updated = { ...prev };
        delete updated[id];
        return updated;
      });

    } catch (err) {
      console.error('Error processing review:', err);
      setError(err instanceof Error ? err.message : 'Failed to process review');
    } finally {
      setProcessingReview(null);
    }
  };

  // Handle AI Dude content validation
  const handleAIDudeValidation = async (id: string) => {
    try {
      setProcessingReview(id);
      setError(null);

      // Find the submission to get content data
      const submission = submissions.find(s => s.id === id);
      if (!submission) {
        throw new Error('Submission not found');
      }

      // Get tool data for validation
      const toolId = submission.id.startsWith('ai_job_') ? submission.id.replace('ai_job_', '') : submission.toolId;

      const toolResponse = await fetch(`/api/admin/tools/${toolId}`, {
        headers: {
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        }
      });

      if (!toolResponse.ok) {
        throw new Error('Failed to get tool data for validation');
      }

      const toolData = await toolResponse.json();
      const tool = toolData.data;

      // Perform AI Dude validation
      const validationResponse = await fetch('/api/admin/content/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        },
        body: JSON.stringify({
          content: tool.generated_content,
          methodology: 'ai_dude',
          toolId: toolId
        })
      });

      if (!validationResponse.ok) {
        const errorData = await validationResponse.json();
        throw new Error(errorData.error || 'AI validation failed');
      }

      const validationResult = await validationResponse.json();
      console.log('AI Dude validation result:', validationResult);

      // Update submission with validation results
      const validationNotes = `AI Dude Validation Results:
Quality Score: ${validationResult.data.qualityScore}/100
Issues: ${validationResult.data.issues.length}
Warnings: ${validationResult.data.warnings.length}
${validationResult.data.issues.length > 0 ? '\nIssues:\n' + validationResult.data.issues.join('\n') : ''}
${validationResult.data.warnings.length > 0 ? '\nWarnings:\n' + validationResult.data.warnings.join('\n') : ''}`;

      // Add validation notes to review notes
      setReviewNotes(prev => ({
        ...prev,
        [id]: (prev[id] || '') + '\n\n' + validationNotes
      }));

      // Show success message
      alert(`AI Dude validation completed!\nQuality Score: ${validationResult.data.qualityScore}/100\nCheck review notes for details.`);

    } catch (err) {
      console.error('Error performing AI Dude validation:', err);
      setError(err instanceof Error ? err.message : 'Failed to perform AI validation');
    } finally {
      setProcessingReview(null);
    }
  };

  const filteredSubmissions = submissions.filter(submission => {
    const matchesStatus = selectedStatus === 'all' || submission.status === selectedStatus;
    const matchesSearch = searchTerm === '' || 
      submission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      submission.submitterName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      submission.category.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesStatus && matchesSearch;
  });

  // Enhanced dashboard component that includes submission selection
  return (
    <div className="min-h-screen bg-zinc-900 text-white font-roboto">
      <div className="container mx-auto px-4 py-8" style={{ maxWidth: 'var(--container-width)' }}>
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Editorial Dashboard</h1>
            <p className="text-gray-300">Manage user submissions and editorial workflow</p>
          </div>
          
          <div className="flex items-center space-x-4">
            <button
              onClick={loadEditorialData}
              className="px-4 py-2 bg-zinc-700 hover:bg-zinc-600 text-white rounded-lg transition-colors"
            >
              Refresh
            </button>
            <a
              href="/admin"
              className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors"
            >
              ← Back to Admin
            </a>
          </div>
        </div>

        {error && (
          <div className="bg-red-900/20 border border-red-500 rounded-lg p-4 mb-6">
            <p className="text-red-400">{error}</p>
          </div>
        )}

        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
              <p className="text-gray-300">Loading editorial dashboard...</p>
            </div>
          </div>
        ) : (
          <>
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mb-8">
              <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">Total Submissions</p>
                    <p className="text-2xl font-bold text-white">{stats.totalSubmissions}</p>
                  </div>
                </div>
              </div>

              <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">Pending Review</p>
                    <p className="text-2xl font-bold text-yellow-400">{stats.pendingReview}</p>
                  </div>
                </div>
              </div>

              <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">Under Review</p>
                    <p className="text-2xl font-bold text-blue-400">{stats.underReview}</p>
                  </div>
                </div>
              </div>

              <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">Approved Today</p>
                    <p className="text-2xl font-bold text-green-400">{stats.approvedToday}</p>
                  </div>
                </div>
              </div>

              <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">Rejected Today</p>
                    <p className="text-2xl font-bold text-red-400">{stats.rejectedToday}</p>
                  </div>
                </div>
              </div>

              <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">Avg Review Time</p>
                    <p className="text-2xl font-bold text-white">{stats.averageReviewTime}h</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Filters and Search */}
            <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-6 mb-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <input
                    type="text"
                    placeholder="Search submissions..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full px-4 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                </div>
                
                <div className="flex items-center gap-4">
                  <select
                    value={selectedStatus}
                    onChange={(e) => setSelectedStatus(e.target.value)}
                    className="px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  >
                    <option value="all">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="under_review">Under Review</option>
                    <option value="approved">Approved</option>
                    <option value="rejected">Rejected</option>
                    <option value="published">Published</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Submissions Table */}
            <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-white">Submissions ({filteredSubmissions.length})</h2>
              </div>

              {filteredSubmissions.length === 0 ? (
                <div className="text-center py-12">
                  <div className="text-gray-400 text-lg mb-2">No submissions found</div>
                  <p className="text-gray-500">Try adjusting your filters or search terms</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredSubmissions.map((submission) => (
                    <div
                      key={submission.id}
                      className="bg-zinc-700 border border-zinc-600 rounded-lg p-6"
                    >
                      <div className="flex justify-between items-start mb-4">
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-white">{submission.name}</h3>
                          <p className="text-gray-300 text-sm mt-1">{submission.description}</p>
                          <div className="flex items-center gap-4 mt-2 text-sm text-gray-400">
                            <span>By: {submission.submitterName}</span>
                            <span>Category: {submission.category}</span>
                            <span>Priority: {submission.priority}</span>
                            {submission.url && (
                              <a
                                href={submission.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-orange-400 hover:text-orange-300 underline"
                              >
                                Visit Tool
                              </a>
                            )}
                          </div>
                        </div>
                        <div className="text-right">
                          <span className={`px-3 py-1 rounded-full text-sm ${
                            submission.status === 'pending' ? 'bg-yellow-400/10 text-yellow-400' :
                            submission.status === 'under_review' ? 'bg-blue-400/10 text-blue-400' :
                            submission.status === 'approved' ? 'bg-green-400/10 text-green-400' :
                            submission.status === 'rejected' ? 'bg-red-400/10 text-red-400' :
                            'bg-gray-400/10 text-gray-400'
                          }`}>
                            {submission.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </span>
                          <div className="text-sm text-gray-400 mt-1">
                            {new Date(submission.submittedAt).toLocaleDateString()}
                          </div>
                        </div>
                      </div>

                      {/* Review Notes Input */}
                      {(submission.status === 'pending' || submission.status === 'under_review') && (
                        <div className="mb-4">
                          <textarea
                            placeholder="Add review notes (optional)..."
                            value={reviewNotes[submission.id] || ''}
                            onChange={(e) => setReviewNotes(prev => ({
                              ...prev,
                              [submission.id]: e.target.value
                            }))}
                            className="w-full px-3 py-2 bg-zinc-600 border border-zinc-500 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-none"
                            rows={2}
                          />
                        </div>
                      )}

                      {/* Review Actions */}
                      {(submission.status === 'pending' || submission.status === 'under_review') && (
                        <div className="flex items-center gap-3 flex-wrap">
                          <button
                            onClick={() => handleReview(submission.id, 'approved', reviewNotes[submission.id])}
                            disabled={processingReview === submission.id}
                            className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-green-800 disabled:opacity-50 text-white rounded-md transition-colors flex items-center gap-2"
                          >
                            {processingReview === submission.id ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            ) : (
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                            )}
                            Approve
                          </button>

                          <button
                            onClick={() => handleReview(submission.id, 'rejected', reviewNotes[submission.id])}
                            disabled={processingReview === submission.id}
                            className="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-red-800 disabled:opacity-50 text-white rounded-md transition-colors flex items-center gap-2"
                          >
                            {processingReview === submission.id ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            ) : (
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            )}
                            Reject
                          </button>

                          <button
                            onClick={() => handleReview(submission.id, 'needs_revision', reviewNotes[submission.id])}
                            disabled={processingReview === submission.id}
                            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:opacity-50 text-white rounded-md transition-colors flex items-center gap-2"
                          >
                            {processingReview === submission.id ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            ) : (
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                              </svg>
                            )}
                            Request Changes
                          </button>

                          {/* AI Dude Content Validation */}
                          <button
                            onClick={() => handleAIDudeValidation(submission.id)}
                            disabled={processingReview === submission.id}
                            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800 disabled:opacity-50 text-white rounded-md transition-colors flex items-center gap-2"
                          >
                            {processingReview === submission.id ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            ) : (
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                            )}
                            AI Validate
                          </button>
                        </div>
                      )}

                      {/* Show review notes if already reviewed */}
                      {submission.reviewNotes && (
                        <div className="mt-4 p-3 bg-zinc-600 rounded-md">
                          <p className="text-sm text-gray-300">
                            <strong>Review Notes:</strong> {submission.reviewNotes}
                          </p>
                          {submission.reviewedBy && submission.reviewedAt && (
                            <p className="text-xs text-gray-400 mt-1">
                              Reviewed by {submission.reviewedBy} on {new Date(submission.reviewedAt).toLocaleDateString()}
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
}
