'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { apiClient } from '@/lib/api';
import { DbTool, ContentStatus } from '@/lib/types';
import { AdminToolsTable } from '@/components/admin/AdminToolsTable';
import { ToolBulkActions } from '@/components/admin/ToolBulkActions';
import { BulkProcessingManager } from '@/components/admin/BulkProcessingManager';
import { ImportExportPanel } from '@/components/admin/tools/ImportExportPanel';

export default function ToolsManagementPage() {
  const router = useRouter();
  const [tools, setTools] = useState<DbTool[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedToolIds, setSelectedToolIds] = useState<string[]>([]);
  const [showImportExport, setShowImportExport] = useState(false);
  const [showBulkProcessing, setShowBulkProcessing] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const toolsResult = await apiClient.getAdminTools({ limit: 100 }, 'aidude_admin_2024_secure_key_xyz789');

      // Validate API response structure
      if (!toolsResult || !toolsResult.data || !Array.isArray(toolsResult.data)) {
        console.error('Invalid tools API response:', toolsResult);
        throw new Error('Invalid API response: tools data is missing or not an array');
      }

      // Transform AITool[] to DbTool[] for admin interface
      const dbTools: DbTool[] = toolsResult.data.map(tool => ({
        id: tool.id,
        name: tool.name,
        slug: tool.slug || '',
        logo_url: tool.logoUrl || '',
        description: tool.description || '',
        short_description: tool.shortDescription || '',
        detailed_description: tool.detailedDescription || '',
        link: tool.link,
        website: tool.website || '',
        category_id: tool.category || '',
        subcategory: tool.subcategory || '',
        company: tool.company || '',
        is_verified: tool.isVerified || false,
        is_claimed: tool.isClaimed || false,
        content_status: tool.contentStatus || 'draft',
        created_at: tool.createdAt || '',
        updated_at: tool.updatedAt || '',
        published_at: tool.publishedAt || '',
        // Enhanced AI System fields
        scraped_data: tool.scrapedData || null,
        ai_generation_status: tool.aiGenerationStatus || 'pending',
        last_scraped_at: tool.lastScrapedAt || '',
        editorial_review_id: tool.editorialReviewId || '',
        ai_generation_job_id: tool.aiGenerationJobId || '',
        submission_type: tool.submissionType || 'admin',
        submission_source: tool.submissionSource || '',
        content_quality_score: tool.contentQualityScore || undefined,
        last_ai_update: tool.lastAiUpdate || '',
        // Additional required DbTool fields
        features: tool.features || null,
        screenshots: tool.screenshots || null,
        pricing: tool.pricing || null,
        social_links: tool.socialLinks || null,
        pros_and_cons: tool.prosAndCons || null,
        haiku: tool.haiku || null,
        hashtags: tool.hashtags || null,
        releases: tool.releases || null,
        claim_info: tool.claimInfo || null,
        meta_title: tool.metaTitle || '',
        meta_description: tool.metaDescription || '',
        generated_content: tool.generatedContent || null,
      }));
      setTools(dbTools);
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteTool = async (toolId: string) => {
    if (!confirm('Are you sure you want to delete this tool?')) return;

    try {
      await apiClient.deleteAdminTool(toolId, 'aidude_admin_2024_secure_key_xyz789');
      setTools(tools.filter(t => t.id !== toolId));
      alert('Tool deleted successfully!');
    } catch (error) {
      alert('Failed to delete tool: ' + (error as Error).message);
    }
  };

  const handleUpdateStatus = async (toolId: string, status: ContentStatus) => {
    try {
      await apiClient.updateAdminTool(toolId, { contentStatus: status }, 'aidude_admin_2024_secure_key_xyz789');
      setTools(tools.map(t => t.id === toolId ? { ...t, content_status: status } : t));
      alert('Tool status updated!');
    } catch (error) {
      alert('Failed to update status: ' + (error as Error).message);
    }
  };

  // Selection handlers
  const handleToolSelection = (toolId: string, selected: boolean) => {
    setSelectedToolIds(prev =>
      selected
        ? [...prev, toolId]
        : prev.filter(id => id !== toolId)
    );
  };

  const handleSelectAll = (selected: boolean) => {
    setSelectedToolIds(selected ? tools.map(t => t.id) : []);
  };

  const handleClearSelection = () => {
    setSelectedToolIds([]);
  };

  // Bulk operations
  const handleBulkStatusUpdate = async (toolIds: string[], status: ContentStatus) => {
    try {
      const result = await apiClient.bulkUpdateToolStatus(toolIds, status, 'aidude_admin_2024_secure_key_xyz789');

      // Update local state
      setTools(tools.map(t =>
        toolIds.includes(t.id) ? { ...t, content_status: status } : t
      ));

      setSelectedToolIds([]);
      alert(result.message || `Successfully updated ${toolIds.length} tools`);
    } catch (error) {
      alert('Failed to update tools: ' + (error as Error).message);
    }
  };

  const handleBulkDelete = async (toolIds: string[]) => {
    try {
      const result = await apiClient.bulkDeleteTools(toolIds, 'aidude_admin_2024_secure_key_xyz789');

      // Update local state
      setTools(tools.filter(t => !toolIds.includes(t.id)));

      setSelectedToolIds([]);
      alert(result.message || `Successfully deleted ${toolIds.length} tools`);
    } catch (error) {
      alert('Failed to delete tools: ' + (error as Error).message);
    }
  };

  const handleImportComplete = () => {
    loadData(); // Reload data after import
    setShowImportExport(false);
  };

  const handleError = (error: string) => {
    alert('Error: ' + error);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-zinc-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading tools management...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">Tool Management</h1>
          <p className="text-gray-400 mt-1">Manage your AI tools directory</p>
        </div>
        <div className="flex flex-wrap gap-3">
          <button
            onClick={() => router.push('/admin/tools/new')}
            className="text-white px-4 py-2 rounded-lg font-medium transition-all duration-200"
            style={{
              backgroundColor: 'rgb(255, 150, 0)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'rgb(255, 170, 30)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)';
            }}
          >
            ➕ Add New Tool
          </button>
          <button
            onClick={() => setShowImportExport(!showImportExport)}
            className="text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 bg-zinc-700 hover:bg-zinc-600"
          >
            {showImportExport ? '📤 Hide Import/Export' : '📥 Import/Export Tools'}
          </button>

          <button
            onClick={() => setShowBulkProcessing(!showBulkProcessing)}
            className="text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 bg-purple-700 hover:bg-purple-600"
          >
            {showBulkProcessing ? '🔄 Hide Bulk Processing' : '⚡ Bulk Processing'}
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-zinc-800 border border-black rounded-lg p-4 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Tools</p>
              <p className="text-xl font-bold text-white">{tools.length}</p>
            </div>
            <div className="text-2xl">🔧</div>
          </div>
        </div>

        <div className="bg-zinc-800 border border-black rounded-lg p-4 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Published</p>
              <p className="text-xl font-bold text-white">
                {tools.filter(t => t.content_status === 'published').length}
              </p>
            </div>
            <div className="text-2xl">✅</div>
          </div>
        </div>

        <div className="bg-zinc-800 border border-black rounded-lg p-4 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Drafts</p>
              <p className="text-xl font-bold text-white">
                {tools.filter(t => t.content_status === 'draft' || !t.content_status).length}
              </p>
            </div>
            <div className="text-2xl">📝</div>
          </div>
        </div>

        <div className="bg-zinc-800 border border-black rounded-lg p-4 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Archived</p>
              <p className="text-xl font-bold text-white">
                {tools.filter(t => t.content_status === 'archived').length}
              </p>
            </div>
            <div className="text-2xl">📦</div>
          </div>
        </div>
      </div>

      {/* Bulk Actions */}
      <ToolBulkActions
        selectedCount={selectedToolIds.length}
        selectedToolIds={selectedToolIds}
        onBulkStatusUpdate={handleBulkStatusUpdate}
        onBulkDelete={handleBulkDelete}
        onClearSelection={handleClearSelection}
      />

      {/* Tools Table */}
      <AdminToolsTable
        tools={tools}
        selectedToolIds={selectedToolIds}
        onToolSelection={handleToolSelection}
        onSelectAll={handleSelectAll}
        onUpdateStatus={handleUpdateStatus}
        onDeleteTool={handleDeleteTool}
      />

      {/* Import/Export Panel */}
      {showImportExport && (
        <ImportExportPanel
          onImportComplete={handleImportComplete}
          onError={handleError}
        />
      )}

      {/* Bulk Processing Panel */}
      {showBulkProcessing && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-zinc-900 rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden">
            <BulkProcessingManager
              onClose={() => setShowBulkProcessing(false)}
            />
          </div>
        </div>
      )}
    </div>
  );
}
