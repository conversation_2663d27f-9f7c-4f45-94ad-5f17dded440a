import { NextRequest, NextResponse } from 'next/server';
import { validate<PERSON><PERSON><PERSON><PERSON> } from '@/lib/auth';
import { AIContentGenerator, GenerationOptions } from '@/lib/ai';

export async function POST(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const {
      url,
      scrapedData,
      pricingData,
      faqData,
      options = {},
      methodology = 'ai_dude', // Default: AI Dude methodology (primary)
      existingToolData = null,   // New: For partial generation
      sectionType = null         // New: For partial generation
    } = await request.json();

    if (!url || !scrapedData) {
      return NextResponse.json(
        { success: false, error: 'URL and scraped data are required' },
        { status: 400 }
      );
    }

    console.log(`Starting ${methodology} AI content generation for: ${url} (AI Dude is now the default methodology)`);

    // Set generation options
    const generationOptions: GenerationOptions = {
      complexity: options.complexity || 'medium',
      priority: options.priority || 'quality',
      contentQuality: options.contentQuality || 70,
      scrapingCost: options.scrapingCost || 0,
      maxRetries: 3
    };

    // Initialize AI content generator
    const aiGenerator = new AIContentGenerator();

    // Handle different methodologies
    let result;

    if (methodology === 'ai_dude') {
      // AI Dude methodology - use raw scraped content
      let content = '';

      if (scrapedData.textContent) {
        content = scrapedData.textContent;
      } else {
        // Fallback: build content from available data
        content = `# ${scrapedData.title || 'AI Tool'}\n\n`;
        if (scrapedData.description) {
          content += `${scrapedData.description}\n\n`;
        }
        if (scrapedData.pricingText) {
          content += `## Pricing\n${scrapedData.pricingText}\n\n`;
        }
        if (scrapedData.faqText) {
          content += `## FAQ\n${scrapedData.faqText}\n\n`;
        }
      }

      // Check if this is partial generation
      if (sectionType && existingToolData) {
        console.log(`Generating ${sectionType} section with AI Dude methodology`);
        result = await aiGenerator.generatePartialContentAIDude(
          sectionType,
          existingToolData,
          content,
          url,
          generationOptions
        );
      } else {
        console.log('Generating complete content with AI Dude methodology');
        result = await aiGenerator.generateContentAIDude(
          content,
          url,
          generationOptions
        );
      }
    } else {
      // Standard methodology - prepare structured content
      let content = `# AI Tool Analysis\n\n`;
      content += `**URL:** ${url}\n\n`;

      if (scrapedData.title) {
        content += `**Title:** ${scrapedData.title}\n\n`;
      }

      if (scrapedData.description) {
        content += `**Description:** ${scrapedData.description}\n\n`;
      }

      if (scrapedData.textContent) {
        content += `**Main Content:**\n${scrapedData.textContent}\n\n`;
      }

      if (scrapedData.pricingText) {
        content += `**Pricing Information:**\n${scrapedData.pricingText}\n\n`;
      }

      if (scrapedData.faqText) {
        content += `**FAQ Content:**\n${scrapedData.faqText}\n\n`;
      }

      // Add structured data if available
      if (pricingData) {
        content += `**Structured Pricing Data:**\n${JSON.stringify(pricingData, null, 2)}\n\n`;
      }

      if (faqData) {
        content += `**Structured FAQ Data:**\n${JSON.stringify(faqData, null, 2)}\n\n`;
      }

      // Use standard generation method
      result = await aiGenerator.generateContent(
        content,
        url,
        generationOptions
      );
    }



    if (!result.success) {
      console.error('AI content generation failed:', result.error);
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      );
    }

    console.log(`Content generation successful using ${result.modelUsed?.provider}/${result.modelUsed?.model}`);
    console.log(`Validation score: ${result.validation?.score || 'N/A'}`);
    console.log(`Token usage:`, result.tokenUsage);

    return NextResponse.json({
      success: true,
      data: {
        aiContent: result.content,
        generationMetadata: {
          provider: result.modelUsed?.provider,
          model: result.modelUsed?.model,
          reasoning: result.modelUsed?.reasoning,
          timestamp: result.timestamp,
          sourceUrl: url,
          tokensUsed: result.tokenUsage?.total_tokens || 0,
          validationScore: result.validation?.score,
          strategyUsed: result.strategyUsed,
          methodology: result.methodology || methodology,
          sectionType: result.sectionType || sectionType
        },
        validation: result.validation
      }
    });

  } catch (error: any) {
    console.error('Enhanced content generation error:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to generate content',
        details: error.stack
      },
      { status: 500 }
    );
  }
}
