'use client';

import React from 'react';
import { Bo<PERSON>, Zap, Info, CheckCircle } from 'lucide-react';

export type ContentMethodology = 'standard' | 'ai_dude';

interface ContentGenerationMethodologyProps {
  onMethodologyChange: (methodology: ContentMethodology) => void;
  currentMethodology: ContentMethodology;
  disabled?: boolean;
  showDescription?: boolean;
}

interface MethodologyOption {
  id: ContentMethodology;
  name: string;
  description: string;
  features: string[];
  icon: React.ComponentType<{ size?: number; className?: string }>;
  color: string;
  recommended?: boolean;
}

const methodologyOptions: MethodologyOption[] = [
  {
    id: 'standard',
    name: 'Standard Generation',
    description: 'Traditional AI content generation with structured prompts and comprehensive analysis.',
    features: [
      'Multi-prompt processing for large content',
      'Structured content analysis',
      'Comprehensive field coverage',
      'Fallback strategies for reliability'
    ],
    icon: Bot,
    color: 'rgb(59, 130, 246)' // Blue
  },
  {
    id: 'ai_dude',
    name: 'AI Dude Methodology',
    description: 'Irreverent, no-BS content generation with simplified field scope and enhanced personality.',
    features: [
      'Snarky, witty AI Dude tone',
      'Simplified database field scope',
      'Raw content processing',
      'Context-aware partial generation',
      'Enhanced SEO optimization'
    ],
    icon: Zap,
    color: 'rgb(255, 150, 0)', // Orange
    recommended: true
  }
];

export function ContentGenerationMethodology({ 
  onMethodologyChange, 
  currentMethodology, 
  disabled = false,
  showDescription = true 
}: ContentGenerationMethodologyProps) {
  
  const handleMethodologySelect = (methodology: ContentMethodology) => {
    if (!disabled) {
      onMethodologyChange(methodology);
    }
  };

  return (
    <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-6">
      <div className="flex items-center gap-2 mb-4">
        <Bot className="w-5 h-5 text-orange-500" />
        <h3 className="text-lg font-semibold text-white">Content Generation Methodology</h3>
      </div>

      {showDescription && (
        <div className="mb-6 p-4 bg-zinc-900 border border-zinc-700 rounded-lg">
          <div className="flex items-start gap-2">
            <Info className="w-4 h-4 text-blue-400 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-gray-300">
              <p className="mb-2">
                Choose the content generation methodology that best fits your needs:
              </p>
              <ul className="list-disc list-inside space-y-1 text-gray-400">
                <li><strong className="text-blue-400">Standard:</strong> Comprehensive, structured content generation</li>
                <li><strong className="text-orange-400">AI Dude:</strong> Irreverent, personality-driven content with simplified scope</li>
              </ul>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {methodologyOptions.map((option) => {
          const Icon = option.icon;
          const isSelected = currentMethodology === option.id;
          const isDisabled = disabled;

          return (
            <div
              key={option.id}
              className={`
                relative border rounded-lg p-4 cursor-pointer transition-all duration-200
                ${isSelected 
                  ? 'border-orange-500 bg-orange-500/10' 
                  : 'border-zinc-600 bg-zinc-900 hover:border-zinc-500 hover:bg-zinc-800'
                }
                ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}
              `}
              onClick={() => handleMethodologySelect(option.id)}
            >
              {/* Recommended Badge */}
              {option.recommended && (
                <div className="absolute -top-2 -right-2 bg-orange-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                  Recommended
                </div>
              )}

              {/* Selection Indicator */}
              {isSelected && (
                <div className="absolute top-3 right-3">
                  <CheckCircle className="w-5 h-5 text-orange-500" />
                </div>
              )}

              <div className="flex items-start gap-3">
                <div 
                  className="p-2 rounded-lg flex-shrink-0"
                  style={{ backgroundColor: `${option.color}20` }}
                >
                  <Icon
                    className={`w-5 h-5 ${option.id === 'ai_dude' ? 'text-orange-500' : 'text-blue-500'}`}
                  />
                </div>

                <div className="flex-1 min-w-0">
                  <h4 className="font-semibold text-white mb-1">
                    {option.name}
                  </h4>
                  
                  <p className="text-sm text-gray-400 mb-3">
                    {option.description}
                  </p>

                  <div className="space-y-1">
                    <div className="text-xs font-medium text-gray-300 mb-2">
                      Key Features:
                    </div>
                    {option.features.map((feature, index) => (
                      <div key={index} className="flex items-center gap-2 text-xs text-gray-400">
                        <div 
                          className="w-1 h-1 rounded-full flex-shrink-0"
                          style={{ backgroundColor: option.color }}
                        />
                        {feature}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Current Selection Summary */}
      {currentMethodology && (
        <div className="mt-4 p-3 bg-zinc-900 border border-zinc-700 rounded-lg">
          <div className="flex items-center gap-2 text-sm">
            <CheckCircle className="w-4 h-4 text-green-400" />
            <span className="text-gray-300">
              Selected: <span className="text-white font-medium">
                {methodologyOptions.find(opt => opt.id === currentMethodology)?.name}
              </span>
            </span>
          </div>
        </div>
      )}
    </div>
  );
}

export default ContentGenerationMethodology;
