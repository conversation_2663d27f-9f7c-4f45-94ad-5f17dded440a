'use client';

import { BulkProcessingOptions } from '@/lib/bulk-processing/bulk-engine';


interface ProcessingOptionsPanelProps {
  options: BulkProcessingOptions;
  onChange: (options: Partial<BulkProcessingOptions>) => void;
  dataPreview: any;
  onStartProcessing: () => void;
  onBack: () => void;
}

/**
 * Processing Options Panel Component
 * 
 * Provides configuration options for bulk processing operations
 * including batch settings, AI provider selection, and processing modes.
 */
export function ProcessingOptionsPanel({
  options,
  onChange,
  dataPreview,
  onStartProcessing,
  onBack,
}: ProcessingOptionsPanelProps) {
  const handleOptionChange = (key: keyof BulkProcessingOptions, value: any) => {
    onChange({ [key]: value });
  };

  const estimatedTime = () => {
    if (!dataPreview?.validItems?.length) return '0 minutes';
    
    const totalItems = dataPreview.validItems.length;
    const batchSize = options.batchSize;
    const delayBetweenBatches = options.delayBetweenBatches / 1000; // Convert to seconds
    const avgProcessingTimePerItem = options.scrapeOnly ? 5 : 15; // seconds
    
    const totalBatches = Math.ceil(totalItems / batchSize);
    const processingTime = totalItems * avgProcessingTimePerItem;
    const delayTime = (totalBatches - 1) * delayBetweenBatches;
    const totalSeconds = processingTime + delayTime;
    
    const minutes = Math.ceil(totalSeconds / 60);
    return `~${minutes} minutes`;
  };

  const estimatedCost = () => {
    if (!dataPreview?.validItems?.length) return '$0.00';
    
    const totalItems = dataPreview.validItems.length;
    const scrapeOnlyCost = 0.02; // $0.02 per item for scraping only
    const fullProcessingCost = 0.15; // $0.15 per item for full AI processing
    
    const costPerItem = options.scrapeOnly ? scrapeOnlyCost : fullProcessingCost;
    const totalCost = totalItems * costPerItem;
    
    return `~$${totalCost.toFixed(2)}`;
  };

  return (
    <div className="bg-zinc-800 border border-black rounded-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-white">Processing Configuration</h2>
        <button
          onClick={onBack}
          className="text-gray-400 hover:text-white transition-colors"
        >
          ← Back to Input
        </button>
      </div>

      <div className="space-y-6">
        {/* Processing Mode */}
        <div>
          <label className="block text-sm font-medium text-white mb-3">
            Processing Mode
          </label>
          <div className="grid grid-cols-1 gap-3">
            <label className="flex items-center space-x-3 p-3 bg-zinc-700 rounded-lg cursor-pointer hover:bg-zinc-600 transition-colors">
              <input
                type="radio"
                name="processingMode"
                checked={options.scrapeOnly}
                onChange={() => {
                  handleOptionChange('scrapeOnly', true);
                  handleOptionChange('generateContent', false);
                }}
                className="text-orange-500 focus:ring-orange-500"
              />
              <div>
                <div className="text-white font-medium">Scrape Only</div>
                <div className="text-gray-400 text-sm">Extract basic data without AI content generation</div>
              </div>
            </label>
            <label className="flex items-center space-x-3 p-3 bg-zinc-700 rounded-lg cursor-pointer hover:bg-zinc-600 transition-colors">
              <input
                type="radio"
                name="processingMode"
                checked={!options.scrapeOnly && options.generateContent}
                onChange={() => {
                  handleOptionChange('scrapeOnly', false);
                  handleOptionChange('generateContent', true);
                }}
                className="text-orange-500 focus:ring-orange-500"
              />
              <div>
                <div className="text-white font-medium">Full Processing</div>
                <div className="text-gray-400 text-sm">Scrape data and generate AI content</div>
              </div>
            </label>
          </div>
        </div>

        {/* AI Provider Selection */}
        {options.generateContent && (
          <div>
            <label className="block text-sm font-medium text-white mb-3">
              AI Provider
            </label>
            <select
              value={options.aiProvider}
              onChange={(e) => handleOptionChange('aiProvider', e.target.value)}
              className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-orange-500"
            >
              <option value="openai">OpenAI (GPT-4)</option>
              <option value="openrouter">OpenRouter (Gemini 2.5 Pro)</option>
            </select>
          </div>
        )}



        {/* Batch Configuration */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Batch Size
            </label>
            <select
              value={options.batchSize}
              onChange={(e) => handleOptionChange('batchSize', parseInt(e.target.value))}
              className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-orange-500"
            >
              <option value={1}>1 item</option>
              <option value={3}>3 items</option>
              <option value={5}>5 items (recommended)</option>
              <option value={10}>10 items</option>
              <option value={20}>20 items</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Priority
            </label>
            <select
              value={options.priority}
              onChange={(e) => handleOptionChange('priority', e.target.value)}
              className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-orange-500"
            >
              <option value="low">Low</option>
              <option value="normal">Normal</option>
              <option value="high">High</option>
            </select>
          </div>
        </div>

        {/* Delay Configuration */}
        <div>
          <label className="block text-sm font-medium text-white mb-2">
            Delay Between Batches: {options.delayBetweenBatches / 1000}s
          </label>
          <input
            type="range"
            min={1000}
            max={10000}
            step={500}
            value={options.delayBetweenBatches}
            onChange={(e) => handleOptionChange('delayBetweenBatches', parseInt(e.target.value))}
            className="w-full h-2 bg-zinc-700 rounded-lg appearance-none cursor-pointer slider"
          />
          <div className="flex justify-between text-xs text-gray-400 mt-1">
            <span>1s (faster)</span>
            <span>10s (safer)</span>
          </div>
        </div>

        {/* Additional Options */}
        <div className="space-y-3">
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={options.skipExisting}
              onChange={(e) => handleOptionChange('skipExisting', e.target.checked)}
              className="rounded border-zinc-600 bg-zinc-700 text-orange-500 focus:ring-orange-500"
            />
            <div>
              <div className="text-white text-sm">Skip Existing Tools</div>
              <div className="text-gray-400 text-xs">Skip URLs that already exist in the database</div>
            </div>
          </label>
          
          {options.generateContent && (
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={options.autoPublish}
                onChange={(e) => handleOptionChange('autoPublish', e.target.checked)}
                className="rounded border-zinc-600 bg-zinc-700 text-orange-500 focus:ring-orange-500"
              />
              <div>
                <div className="text-white text-sm">Auto-Publish</div>
                <div className="text-gray-400 text-xs">Automatically publish tools after processing</div>
              </div>
            </label>
          )}
        </div>

        {/* Estimates */}
        <div className="bg-zinc-700 rounded-lg p-4">
          <h3 className="text-white font-medium mb-3">Processing Estimates</h3>
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div className="text-center">
              <div className="text-blue-400 font-bold text-lg">{dataPreview?.validItems?.length || 0}</div>
              <div className="text-gray-400">Items to Process</div>
            </div>
            <div className="text-center">
              <div className="text-yellow-400 font-bold text-lg">{estimatedTime()}</div>
              <div className="text-gray-400">Estimated Time</div>
            </div>
            <div className="text-center">
              <div className="text-green-400 font-bold text-lg">{estimatedCost()}</div>
              <div className="text-gray-400">Estimated Cost</div>
            </div>
          </div>
        </div>

        {/* Start Processing Button */}
        <button
          onClick={onStartProcessing}
          className="w-full bg-orange-500 hover:bg-orange-600 text-white font-medium py-3 px-4 rounded-lg transition-colors"
        >
          Start Bulk Processing
        </button>
      </div>
    </div>
  );
}
