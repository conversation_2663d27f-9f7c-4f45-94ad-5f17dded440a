import { MultiPromptContext } from './types';

export class PromptManager {
  
  /**
   * System prompts for different content generation scenarios
   */
  static readonly SYSTEM_PROMPTS = {
    toolAnalysis: `You are an expert AI tool analyst with deep knowledge of the AI industry. Your task is to analyze AI tools and generate comprehensive, accurate content that matches our database schema.

CRITICAL REQUIREMENTS:
- Generate content in JSON format matching the exact database schema
- Use an irreverent, humorous tone similar to ThePornDude style
- Be factual but entertaining in descriptions
- Include specific technical details and use cases
- Generate 3-8 features, 3-10 pros/cons, 3-6 Q&As
- Keep descriptions under 500 characters, detailed descriptions 150-300 words
- Create engaging haikus and relevant hashtags
- Classify pricing accurately: Free, Paid, Freemium, or Open Source

SCHEMA REQUIREMENTS:
{
  "detailed_description": "string (150-300 words)",
  "features": ["array of 3-8 feature strings"],
  "pricing": {
    "type": "Free|Paid|Freemium|Open Source",
    "plans": [{"name": "string", "price": "string", "features": ["array"]}]
  },
  "pros_and_cons": {
    "pros": ["array of 3-10 pros"],
    "cons": ["array of 3-10 cons"]
  },
  "haiku": {
    "lines": ["line1", "line2", "line3"],
    "theme": "string"
  },
  "hashtags": ["array of 5-10 relevant hashtags"],
  "social_links": {
    "twitter": "url or null",
    "linkedin": "url or null",
    "github": "url or null"
  }
}`,

    contentCompletion: `You are continuing content generation for an AI tool. Previous context has been provided. Generate the remaining content sections while maintaining consistency with the established tone and style.

CONTINUATION REQUIREMENTS:
- Maintain the same irreverent, humorous tone
- Ensure consistency with previously generated content
- Complete any missing sections from the schema
- Do not repeat information from previous chunks
- Focus on the specific sections requested`,

    contentValidation: `You are a content quality validator. Review the generated content for accuracy, completeness, and adherence to our style guidelines.

VALIDATION CRITERIA:
- Schema compliance (all required fields present)
- Content quality (engaging, informative, accurate)
- Tone consistency (irreverent but professional)
- Technical accuracy (features and capabilities)
- Pricing accuracy (correct classification and details)`
  };

  /**
   * Build a single prompt for complete content generation
   */
  static buildSinglePrompt(
    scrapedContent: string,
    toolUrl: string,
    options: {
      includeSchema?: boolean;
      customInstructions?: string;
    } = {}
  ): string {
    const { includeSchema = true, customInstructions = '' } = options;

    const prompt = `${this.SYSTEM_PROMPTS.toolAnalysis}

Tool URL: ${toolUrl}
Scraped Content:
${scrapedContent}

${customInstructions}

Generate comprehensive content for this AI tool following the exact JSON schema requirements above. Ensure all required fields are included and maintain the irreverent, engaging tone throughout.`;

    return prompt;
  }

  /**
   * Build multi-prompt for large content processing
   */
  static buildMultiPrompt(
    chunk: string,
    context: MultiPromptContext,
    options: {
      customInstructions?: string;
    } = {}
  ): string {
    const { 
      isFirstChunk, 
      isLastChunk, 
      chunkIndex, 
      totalChunks, 
      accumulatedContext, 
      toolUrl 
    } = context;
    
    const { customInstructions = '' } = options;
    
    let prompt = '';
    
    if (isFirstChunk) {
      prompt = `${this.SYSTEM_PROMPTS.toolAnalysis}

MULTI-CHUNK PROCESSING: This is chunk ${chunkIndex + 1} of ${totalChunks}.
Generate initial content sections and prepare for continuation.

Tool URL: ${toolUrl}
Scraped Content Chunk:
${chunk}

${customInstructions}

Generate the following sections for this chunk:
- detailed_description (if sufficient information available)
- features (partial list, will be completed in subsequent chunks)
- pricing (if pricing information is in this chunk)

Respond with JSON and include a "continuation_needed" field indicating what sections need completion.`;

    } else if (isLastChunk) {
      prompt = `${this.SYSTEM_PROMPTS.contentCompletion}

FINAL CHUNK: Complete all remaining sections and finalize the content.

Previous Context:
${accumulatedContext}

Final Content Chunk:
${chunk}

${customInstructions}

Complete the content generation with all remaining sections:
- Complete features list
- pros_and_cons
- haiku
- hashtags
- social_links
- Any missing sections

Provide the final complete JSON response.`;

    } else {
      prompt = `${this.SYSTEM_PROMPTS.contentCompletion}

MIDDLE CHUNK: Continue content generation with this additional information.

Previous Context:
${accumulatedContext}

Additional Content Chunk:
${chunk}

${customInstructions}

Continue building the content, focusing on:
- Additional features
- More detailed information
- Pricing details (if found)

Respond with JSON and indicate what sections still need completion.`;
    }
    
    return prompt;
  }

  /**
   * Build validation prompt for content quality checking
   */
  static buildValidationPrompt(
    generatedContent: any,
    originalContent: string,
    toolUrl: string
  ): string {
    return `${this.SYSTEM_PROMPTS.contentValidation}

Original Tool URL: ${toolUrl}
Original Scraped Content: ${originalContent.substring(0, 1000)}...

Generated Content to Validate:
${JSON.stringify(generatedContent, null, 2)}

Please validate this content and provide:
1. Overall quality score (0-100)
2. List of any issues found
3. Suggestions for improvement
4. Compliance with schema requirements

Respond with JSON format:
{
  "quality_score": number,
  "issues": ["array of issues"],
  "suggestions": ["array of suggestions"],
  "schema_compliance": boolean,
  "tone_consistency": boolean,
  "technical_accuracy": boolean
}`;
  }

  /**
   * Build prompt for content enhancement/refinement
   */
  static buildEnhancementPrompt(
    existingContent: any,
    enhancementType: 'features' | 'description' | 'pricing' | 'pros_cons',
    additionalContext?: string
  ): string {
    return `You are enhancing existing AI tool content. Focus on improving the ${enhancementType} section while maintaining consistency with the existing content.

Existing Content:
${JSON.stringify(existingContent, null, 2)}

${additionalContext ? `Additional Context:\n${additionalContext}\n` : ''}

Enhancement Instructions:
${this.getEnhancementInstructions(enhancementType)}

Provide the enhanced content in JSON format, including only the sections that have been improved.`;
  }

  private static getEnhancementInstructions(type: string): string {
    const instructions = {
      features: `Enhance the features list by:
- Adding more specific technical details
- Including use cases and benefits
- Ensuring 3-8 comprehensive features
- Making each feature clear and actionable`,

      description: `Enhance the detailed description by:
- Adding more engaging and humorous elements
- Including specific technical capabilities
- Maintaining 150-300 word limit
- Improving readability and flow`,

      pricing: `Enhance the pricing information by:
- Clarifying pricing tiers and features
- Adding value propositions for each plan
- Ensuring accurate pricing classification
- Including any free trial or freemium details`,

      pros_cons: `Enhance the pros and cons by:
- Adding more specific and actionable points
- Balancing positive and negative aspects
- Including user experience insights
- Ensuring 3-10 items in each category`
    };

    return instructions[type as keyof typeof instructions] || 'Enhance the content for better quality and accuracy.';
  }

  /**
   * Extract and clean JSON from AI response
   */
  static extractJsonFromResponse(response: string): any {
    try {
      // Remove markdown code blocks if present
      let cleanContent = response.trim();
      if (cleanContent.startsWith('```json')) {
        cleanContent = cleanContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanContent.startsWith('```')) {
        cleanContent = cleanContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      return JSON.parse(cleanContent);
    } catch (error) {
      throw new Error(`Failed to parse AI response as JSON: ${error}. Content: ${response.substring(0, 200)}...`);
    }
  }

  /**
   * Combine results from multiple prompt processing
   */
  static combineMultiPromptResults(results: any[]): any {
    if (results.length === 1) {
      return results[0];
    }

    // Combine results from multiple chunks
    const combined: any = {};
    
    for (const result of results) {
      if (typeof result === 'object' && result !== null) {
        Object.assign(combined, result);
        
        // Special handling for arrays - concatenate unique items
        if (result.features && Array.isArray(result.features)) {
          combined.features = [...new Set([...(combined.features || []), ...result.features])];
        }
        
        if (result.hashtags && Array.isArray(result.hashtags)) {
          combined.hashtags = [...new Set([...(combined.hashtags || []), ...result.hashtags])];
        }
      }
    }

    return combined;
  }

  /**
   * Calculate token count for prompt optimization
   */
  static calculateTokenCount(text: string): number {
    // Approximate token calculation (1 token ≈ 4 characters for English)
    return Math.ceil(text.length / 4);
  }

  /**
   * Optimize prompt length for model constraints
   */
  static optimizePromptLength(
    prompt: string,
    maxTokens: number,
    preserveSections: string[] = ['CRITICAL REQUIREMENTS', 'SCHEMA REQUIREMENTS']
  ): string {
    const currentTokens = this.calculateTokenCount(prompt);

    if (currentTokens <= maxTokens) {
      return prompt;
    }

    // If prompt is too long, try to preserve critical sections
    const lines = prompt.split('\n');
    const preservedLines: string[] = [];
    const optionalLines: string[] = [];

    let inPreservedSection = false;

    for (const line of lines) {
      const isPreservedSection = preserveSections.some(section => line.includes(section));

      if (isPreservedSection) {
        inPreservedSection = true;
      }

      if (inPreservedSection || isPreservedSection) {
        preservedLines.push(line);
      } else {
        optionalLines.push(line);
      }

      if (line.trim() === '' && inPreservedSection) {
        inPreservedSection = false;
      }
    }

    // Start with preserved content and add optional content until limit
    let optimizedPrompt = preservedLines.join('\n');

    for (const line of optionalLines) {
      const testPrompt = optimizedPrompt + '\n' + line;
      if (this.calculateTokenCount(testPrompt) <= maxTokens) {
        optimizedPrompt = testPrompt;
      } else {
        break;
      }
    }

    return optimizedPrompt;
  }

  // ===== AI DUDE METHODOLOGY METHODS =====

  /**
   * Build AI Dude system prompt with schema injection using database template
   */
  static async buildAIDudeSystemPrompt(databaseSchema: any): Promise<string> {
    try {
      // Try to get template from database first
      const { createClient } = await import('@supabase/supabase-js');
      const supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!
      );

      const { data: template, error } = await supabase
        .from('system_configuration')
        .select('config_value')
        .eq('config_key', 'prompt_ai_dude_complete_system')
        .single();

      if (!error && template?.config_value?.template) {
        // Use database template with schema injection
        const schemaString = JSON.stringify(databaseSchema, null, 2);
        return template.config_value.template.replace('{DATABASE_SCHEMA}', schemaString);
      }
    } catch (error) {
      console.warn('Failed to load AI Dude template from database, using fallback:', error);
    }

    // Enhanced fallback template with releases field
    const schemaString = JSON.stringify(databaseSchema, null, 2);
    return `You are AI Dude, the irreverent, no-BS curator of AI tools. Your job is to read raw Markdown about a single AI tool and spit out a JSON object that matches this exact schema:

${schemaString}

**Tone rules:**
- Always write like a snarky, witty AI Dude.
- Keep it punchy: no corporate sugarcoating.
- Use contractions, slang, and street-smart humor.
- Never apologize or say I am sorry.
- Write description as a one-sentence hook.
- Write short_description as a punchy card summary (max 150 chars).
- Make detailed_description engaging and informative (150-300 words).
- Create meta_title and meta_description that are SEO-optimized but still snarky.
- Ensure category_confidence is 0.90+ if obvious; 0.80-0.75 if guessing.

**Field Requirements:**
- **Required fields**: name, description, short_description, detailed_description, company, category_primary, features, pricing, pros_and_cons, faqs, hashtags, meta_title, meta_description
- **Optional fields**: Fill when information is available in scraped content
- **FAQ generation**: Create 3-5 relevant Q&As in AI Dude style with complete metadata
- **SEO optimization**: meta_title (max 60 chars), meta_description (150-160 chars), meta_keywords (future implementation)
- **Excluded fields**: logo_url, website, screenshots, claim_info, generated_content (handled by other systems)

**VERY IMPORTANT:**
- Output exactly one JSON object.
- Do not wrap it in backticks or code fences.
- Do not add extra fields or comments.
- If any section is missing, use appropriate defaults: empty string for strings, [] for arrays, {} for objects.
- Always format dates as YYYY-MM-DD.
- Generate UUIDs for FAQ entries.
- Include complete generated_content metadata.

Now read the user content and produce the complete JSON with ALL required fields.`;
  }

  /**
   * Build AI Dude user prompt (raw content only)
   */
  static buildAIDudeUserPrompt(scrapedContent: string, toolUrl: string): string {
    return scrapedContent; // Pure content as per AI Dude methodology
  }

  /**
   * Get simplified database schema for AI Dude prompts (AI-generated fields only)
   */
  static getAIDudeDatabaseSchema(): any {
    return {
      // Core identification fields
      name: "string (required, max 255 chars)",
      description: "string (required, brief description)",
      short_description: "string (required, max 150 chars for cards)",
      detailed_description: "string (required, 150-300 words)",

      // Company and categorization
      company: "string (required, company/organization name)",
      category_primary: "string (required, primary category)",
      category_secondary: "string (optional, secondary category)",
      category_confidence: "number (required, 0.0-1.0 confidence score)",

      // Core content fields
      features: ["array of 3-8 feature strings (required)"],
      pricing: {
        type: "Free|Paid|Freemium|Open Source (required)",
        plans: [{"name": "string", "price": "string", "features": ["array"]}],
        details: "string (pricing description)"
      },
      pros_and_cons: {
        pros: ["array of 3-10 pros (required)"],
        cons: ["array of 3-10 cons (required)"]
      },

      // Social and external links
      social_links: {
        twitter: "url or null",
        linkedin: "url or null",
        github: "url or null",
        facebook: "url or null",
        youtube: "url or null"
      },

      // Additional content (from implementation guide requirements)
      hashtags: ["array of 5-10 hashtags (required)"],
      tooltip: "string (under 100 chars)",
      haiku: {
        lines: ["line1", "line2", "line3"],
        theme: "string"
      },
      releases: [
        {
          version: "string",
          releaseDate: "YYYY-MM-DD",
          changes: ["array of changes"]
        }
      ],

      // FAQ system with complete structure
      faqs: [
        {
          id: "uuid",
          question: "string (required)",
          answer: "string (required)",
          category: "general|pricing|features|support|getting-started",
          displayOrder: "number",
          priority: "number (1-10)",
          isActive: true,
          isFeatured: "boolean",
          source: "ai_generated",
          sourceMetadata: {
            aiModel: "string",
            confidence: "number (0.0-1.0)"
          },
          metaKeywords: "string"
        }
      ],

      // SEO fields
      meta_title: "string (required, max 60 chars, SEO optimized)",
      meta_description: "string (required, 150-160 chars, SEO optimized)",
      meta_keywords: "string (future implementation, SEO keywords comma-separated)"
    };
  }

  /**
   * Get fields excluded from AI generation
   */
  static getExcludedFields(): any {
    return {
      logo_url: "Handled by media upload system",
      website: "Provided during tool submission",
      screenshots: "Handled by media upload system",
      claim_info: "Handled by claiming system",
      generated_content: "System metadata field"
    };
  }

  /**
   * Process AI Dude response and map to database schema (AI-generated fields only)
   */
  static processAIDudeResponse(aiResponse: any): any {
    const mapped: Record<string, any> = {
      // Core identification fields
      name: aiResponse.name || '',
      description: aiResponse.description || '',
      short_description: aiResponse.short_description || '',
      detailed_description: aiResponse.detailed_description || '',

      // Company and categorization
      company: aiResponse.company || '',
      category_id: aiResponse.category_primary || null,
      subcategory: aiResponse.category_secondary || null,

      // Core content fields
      features: aiResponse.features || [],
      pricing: aiResponse.pricing || { type: 'unknown', plans: [], details: '' },
      pros_and_cons: aiResponse.pros_and_cons || { pros: [], cons: [] },

      // Social and external links
      social_links: aiResponse.social_links || {},

      // Additional content
      hashtags: aiResponse.hashtags || [],
      tooltip: aiResponse.tooltip || '',
      haiku: aiResponse.haiku || { lines: [], theme: '' },
      releases: aiResponse.releases || [],

      // FAQ system with complete structure
      faqs: aiResponse.faqs || [],

      // SEO fields
      meta_title: aiResponse.meta_title || '',
      meta_description: aiResponse.meta_description || '',
      meta_keywords: aiResponse.meta_keywords || '' // Future implementation
    };

    // Add AI generation metadata (system field, not AI-generated)
    mapped.generated_content = {
      methodology: 'ai_dude',
      generated_at: new Date().toISOString(),
      model_used: 'unknown', // Set by system
      quality_score: 0, // Calculated by system
      validation_status: 'pending',
      category_confidence: aiResponse.category_confidence || 0.5,
      schema_version: '2.0',
      fields_generated: Object.keys(mapped).filter(key =>
        key !== 'generated_content' && // Exclude system field
        mapped[key] !== '' && mapped[key] !== null &&
        (Array.isArray(mapped[key]) ? mapped[key].length > 0 : true)
      ),
      excluded_fields: ['logo_url', 'website', 'screenshots', 'claim_info'] // Fields not AI-generated
    };

    return mapped;
  }

  /**
   * Process partial AI Dude response for specific sections
   */
  static processPartialAIDudeResponse(aiResponse: any, sectionType: string, existingData: any = {}): any {
    const updated = { ...existingData };

    // Update only the specific section
    switch (sectionType) {
      case 'features':
        updated.features = aiResponse.features || [];
        break;
      case 'pricing':
        updated.pricing = aiResponse.pricing || { type: 'unknown', plans: [], details: '' };
        break;
      case 'pros_cons':
        updated.pros_and_cons = aiResponse.pros_and_cons || { pros: [], cons: [] };
        break;
      case 'seo':
        updated.meta_title = aiResponse.meta_title || '';
        updated.meta_description = aiResponse.meta_description || '';
        break;
      case 'faqs':
        updated.faqs = aiResponse.faqs || [];
        break;
      case 'social':
        updated.social_links = aiResponse.social_links || {};
        break;
      case 'company':
        updated.company = aiResponse.company || '';
        updated.website = aiResponse.website || updated.website;
        break;
      case 'content':
        updated.description = aiResponse.description || updated.description;
        updated.short_description = aiResponse.short_description || updated.short_description;
        updated.detailed_description = aiResponse.detailed_description || updated.detailed_description;
        break;
    }

    // Update generation metadata
    updated.generated_content = {
      ...updated.generated_content,
      last_updated: new Date().toISOString(),
      last_section_updated: sectionType,
      methodology: 'ai_dude_partial',
      partial_updates: [...(updated.generated_content?.partial_updates || []), {
        section: sectionType,
        updated_at: new Date().toISOString(),
        quality_score: aiResponse.quality_score || 0
      }]
    };

    return updated;
  }

  /**
   * Build partial generation prompt with context
   */
  static buildPartialAIDudePrompt(
    sectionType: string,
    existingToolData: any,
    scrapedContent: string,
    toolUrl: string,
    sectionRequirements?: string
  ): string {
    return `You are "AI Dude," the irreverent, no-BS curator of AI tools. Generate ONLY the ${sectionType} section for this tool in your signature snarky style.

**Existing Tool Data (for context):**
${JSON.stringify(existingToolData, null, 2)}

**New Scraped Content:**
${scrapedContent}

**Tool URL:** ${toolUrl}

**Section to Generate:** ${sectionType}

**Section Requirements:**
${sectionRequirements || this.getSectionRequirements(sectionType)}

**Instructions:**
- Use the existing tool data for context and consistency
- Focus ONLY on generating the ${sectionType} section
- Maintain consistency with existing tone and style
- If updating existing content, improve and enhance it
- Keep the irreverent, witty "AI Dude" voice throughout
- For FAQs: Include complete metadata structure with UUIDs
- For SEO: Optimize for search while maintaining personality

Output only the requested section in JSON format matching the database schema.`;
  }

  /**
   * Get section-specific requirements for partial generation
   */
  private static getSectionRequirements(sectionType: string): string {
    const requirements = {
      features: "Generate 3-8 comprehensive features that are specific and actionable",
      pricing: "Determine pricing type (Free/Paid/Freemium/Open Source) and create detailed plans if available",
      pros_cons: "Generate 3-10 honest pros and cons that are balanced and specific",
      seo: "Create compelling meta_title (max 60 chars) and meta_description (150-160 chars) that are SEO-optimized",
      faqs: "Create 3-5 relevant Q&As with complete metadata structure including UUIDs",
      social: "Extract and format social media links (Twitter, LinkedIn, GitHub, Facebook, YouTube)",
      company: "Identify company/organization name and any additional company information",
      content: "Generate engaging description, short_description (max 150 chars), and detailed_description (150-300 words)"
    };

    return requirements[sectionType as keyof typeof requirements] || "Generate the requested section with high quality and consistency";
  }
}
