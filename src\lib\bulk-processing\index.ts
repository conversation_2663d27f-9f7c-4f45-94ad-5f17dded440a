/**
 * Bulk Processing Module
 * Main exports for the bulk processing system
 */

// Core engine and managers
export { BulkProcessingEngine, getBulkProcessingEngine } from './bulk-engine';
export { BatchManager, getBatchManager } from './batch-manager';

// File processors
export { 
  TextFileProcessor, 
  JSONFileProcessor, 
  ManualEntryProcessor,
  FILE_SPECS 
} from './file-processors';

// Types and interfaces
export type {
  FileProcessingResult,
  ProcessedFileData,
  InvalidItem,
  ValidationResult,
  JSONFormat,
} from './file-processors';

export type {
  BulkProcessingOptions,
  BulkJobResult,
  BatchResult,
} from './bulk-engine';

export type {
  BatchConfiguration,
  BatchMetrics,
  BatchStrategy,
} from './batch-manager';

// Utility functions
export function validateBulkProcessingOptions(options: any): {
  isValid: boolean;
  errors: string[];
  sanitized: any;
} {
  const errors: string[] = [];
  const sanitized: any = {};

  // Validate batchSize
  if (options.batchSize !== undefined) {
    const batchSize = parseInt(options.batchSize);
    if (isNaN(batchSize) || batchSize < 1 || batchSize > 20) {
      errors.push('batchSize must be a number between 1 and 20');
    } else {
      sanitized.batchSize = batchSize;
    }
  }

  // Validate delayBetweenBatches
  if (options.delayBetweenBatches !== undefined) {
    const delay = parseInt(options.delayBetweenBatches);
    if (isNaN(delay) || delay < 0 || delay > 60000) {
      errors.push('delayBetweenBatches must be a number between 0 and 60000 (1 minute)');
    } else {
      sanitized.delayBetweenBatches = delay;
    }
  }

  // Validate retryAttempts
  if (options.retryAttempts !== undefined) {
    const retries = parseInt(options.retryAttempts);
    if (isNaN(retries) || retries < 0 || retries > 5) {
      errors.push('retryAttempts must be a number between 0 and 5');
    } else {
      sanitized.retryAttempts = retries;
    }
  }

  // Validate aiProvider
  if (options.aiProvider !== undefined) {
    if (!['openai', 'openrouter'].includes(options.aiProvider)) {
      errors.push('aiProvider must be either "openai" or "openrouter"');
    } else {
      sanitized.aiProvider = options.aiProvider;
    }
  }

  // Validate priority
  if (options.priority !== undefined) {
    if (!['low', 'normal', 'high'].includes(options.priority)) {
      errors.push('priority must be "low", "normal", or "high"');
    } else {
      sanitized.priority = options.priority;
    }
  }

  // Validate boolean options
  const booleanOptions = ['skipExisting', 'scrapeOnly', 'generateContent', 'autoPublish'];
  booleanOptions.forEach(option => {
    if (options[option] !== undefined) {
      if (typeof options[option] !== 'boolean') {
        errors.push(`${option} must be a boolean`);
      } else {
        sanitized[option] = options[option];
      }
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    sanitized,
  };
}

export function formatBulkJobProgress(job: any): {
  percentage: number;
  status: string;
  summary: string;
  details: {
    total: number;
    processed: number;
    successful: number;
    failed: number;
    remaining: number;
  };
} {
  const total = job.totalItems || 0;
  const processed = job.processedItems || 0;
  const successful = job.successfulItems || 0;
  const failed = job.failedItems || 0;
  const remaining = total - processed;
  const percentage = total > 0 ? Math.round((processed / total) * 100) : 0;

  const status = job.status || 'unknown';
  let summary = '';

  switch (status) {
    case 'pending':
      summary = `Waiting to start processing ${total} items`;
      break;
    case 'processing':
      summary = `Processing ${processed}/${total} items (${percentage}% complete)`;
      break;
    case 'completed':
      summary = `Completed processing ${total} items (${successful} successful, ${failed} failed)`;
      break;
    case 'failed':
      summary = `Failed after processing ${processed}/${total} items`;
      break;
    case 'cancelled':
      summary = `Cancelled after processing ${processed}/${total} items`;
      break;
    case 'paused':
      summary = `Paused at ${processed}/${total} items (${percentage}% complete)`;
      break;
    default:
      summary = `Status: ${status}`;
  }

  return {
    percentage,
    status,
    summary,
    details: {
      total,
      processed,
      successful,
      failed,
      remaining,
    },
  };
}

export function estimateProcessingTime(
  totalItems: number,
  batchSize: number = 5,
  delayBetweenBatches: number = 2000,
  averageItemTime: number = 30000 // 30 seconds per item
): {
  estimatedMinutes: number;
  estimatedHours: number;
  formattedTime: string;
} {
  const batches = Math.ceil(totalItems / batchSize);
  const processingTime = totalItems * averageItemTime;
  const delayTime = (batches - 1) * delayBetweenBatches;
  const totalTime = processingTime + delayTime;

  const estimatedMinutes = Math.round(totalTime / 60000);
  const estimatedHours = Math.round(estimatedMinutes / 60 * 10) / 10;

  let formattedTime = '';
  if (estimatedHours >= 1) {
    formattedTime = `${estimatedHours} hours`;
  } else if (estimatedMinutes >= 1) {
    formattedTime = `${estimatedMinutes} minutes`;
  } else {
    formattedTime = 'Less than 1 minute';
  }

  return {
    estimatedMinutes,
    estimatedHours,
    formattedTime,
  };
}

export function calculateCostEstimate(
  totalItems: number,
  options: {
    scrapeOnly?: boolean;
    generateContent?: boolean;
    aiProvider?: 'openai' | 'openrouter';
  } = {}
): {
  scrapingCost: number;
  aiCost: number;
  totalCost: number;
  currency: string;
} {
  // Rough cost estimates (these should be updated based on actual API pricing)
  const SCRAPING_COST_PER_ITEM = 0.01; // $0.01 per URL scraped
  const AI_COST_PER_ITEM = {
    openai: 0.05, // $0.05 per item for GPT-4
    openrouter: 0.03, // $0.03 per item for Gemini 2.5 Pro
  };

  let scrapingCost = 0;
  let aiCost = 0;

  // Calculate scraping cost
  if (options.scrapeOnly !== false) {
    scrapingCost = totalItems * SCRAPING_COST_PER_ITEM;
  }

  // Calculate AI cost
  if (options.generateContent !== false) {
    const aiProvider = options.aiProvider || 'openai';
    aiCost = totalItems * AI_COST_PER_ITEM[aiProvider];
  }

  const totalCost = scrapingCost + aiCost;

  return {
    scrapingCost,
    aiCost,
    totalCost,
    currency: 'USD',
  };
}

// Default processing options
export const DEFAULT_BULK_PROCESSING_OPTIONS = {
  batchSize: 5,
  delayBetweenBatches: 2000,
  retryAttempts: 3,
  aiProvider: 'openai' as const,
  skipExisting: false,
  scrapeOnly: false,
  generateContent: true,
  autoPublish: false,
  priority: 'normal' as const,
  methodology: 'ai_dude' as const, // AI Dude is now the default
};

// File upload limits
export const BULK_PROCESSING_LIMITS = {
  maxFileSize: {
    text: 10 * 1024 * 1024, // 10MB
    json: 50 * 1024 * 1024, // 50MB
  },
  maxItems: {
    text: 1000,
    json: 1000,
    manual: 1000,
  },
  maxConcurrentJobs: 5,
  maxBatchSize: 20,
  minBatchSize: 1,
} as const;
