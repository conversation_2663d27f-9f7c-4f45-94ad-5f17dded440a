import { Job, <PERSON><PERSON><PERSON><PERSON>, ToolSubmissionJobData, JobType } from '../types';
import { JobManager } from '../job-manager';
import { createClient } from '@supabase/supabase-js';

export class ToolSubmissionHandler implements JobHandler {
  private _supabase: any = null;
  private jobManager = new JobManager();

  private get supabase() {
    if (!this._supabase) {
      this._supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!
      );
    }
    return this._supabase;
  }

  async handle(job: Job): Promise<any> {
    const data = job.data as ToolSubmissionJobData;
    
    try {
      // Step 1: Scrape the website
      const scrapingResult = await this.scrapeWebsite(data.url);
      
      // Step 2: Generate AI content
      const contentResult = await this.generateContent(data.url, scrapingResult, data.methodology || 'ai_dude');
      
      // Step 3: Create tool draft in database
      const toolResult = await this.createToolDraft(data, contentResult);
      
      // Step 4: Send notification email
      await this.sendNotificationEmail(data, toolResult);
      
      return {
        success: true,
        toolId: toolResult.id,
        message: 'Tool submission processed successfully',
      };
    } catch (error) {
      console.error('Tool submission processing failed:', error);
      
      // Send error notification
      await this.sendErrorNotification(data, error);
      
      throw error;
    }
  }

  private async scrapeWebsite(url: string): Promise<any> {
    const scrapingJob = await this.jobManager.createJob(JobType.WEB_SCRAPING, {
      url,
      options: {
        timeout: 30000,
        extractImages: true,
        extractLinks: true,
      },
    });

    // Wait for scraping job to complete
    return new Promise((resolve, reject) => {
      const checkJob = async () => {
        const job = await this.jobManager.getJob(scrapingJob.id);
        if (!job) {
          reject(new Error('Scraping job not found'));
          return;
        }

        if (job.status === 'completed') {
          resolve(job.result);
        } else if (job.status === 'failed') {
          reject(new Error(job.error || 'Scraping failed'));
        } else {
          setTimeout(checkJob, 2000); // Check every 2 seconds
        }
      };

      checkJob();
    });
  }

  private async generateContent(url: string, scrapedData: any, methodology: 'ai_dude' | 'standard' = 'ai_dude'): Promise<any> {
    const contentJob = await this.jobManager.createJob(JobType.CONTENT_GENERATION, {
      url,
      scrapedData,
      methodology, // Pass methodology to content generation job
    });

    // Wait for content generation job to complete
    return new Promise((resolve, reject) => {
      const checkJob = async () => {
        const job = await this.jobManager.getJob(contentJob.id);
        if (!job) {
          reject(new Error('Content generation job not found'));
          return;
        }

        if (job.status === 'completed') {
          resolve(job.result);
        } else if (job.status === 'failed') {
          reject(new Error(job.error || 'Content generation failed'));
        } else {
          setTimeout(checkJob, 2000); // Check every 2 seconds
        }
      };

      checkJob();
    });
  }

  private async createToolDraft(
    submissionData: ToolSubmissionJobData,
    contentData: any
  ): Promise<any> {
    const { data, error } = await this.supabase
      .from('tools')
      .insert({
        name: submissionData.name,
        url: submissionData.url,
        description: contentData.description || submissionData.description,
        content_status: 'draft',
        generated_content: contentData,
        submitter_email: submissionData.submitterEmail,
        submitter_name: submissionData.submitterName,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create tool draft: ${error.message}`);
    }

    return data;
  }

  private async sendNotificationEmail(
    submissionData: ToolSubmissionJobData,
    toolResult: any
  ): Promise<void> {
    await this.jobManager.createJob(JobType.EMAIL_NOTIFICATION, {
      to: submissionData.submitterEmail,
      subject: 'Tool Submission Received - AI Dude Directory',
      template: 'tool-submission-received',
      data: {
        toolName: submissionData.name,
        toolUrl: submissionData.url,
        submitterName: submissionData.submitterName,
        toolId: toolResult.id,
      },
    });

    // Also notify admin
    if (process.env.ADMIN_EMAIL) {
      await this.jobManager.createJob(JobType.EMAIL_NOTIFICATION, {
        to: process.env.ADMIN_EMAIL,
        subject: 'New Tool Submission - AI Dude Directory',
        template: 'admin-tool-submission',
        data: {
          toolName: submissionData.name,
          toolUrl: submissionData.url,
          submitterEmail: submissionData.submitterEmail,
          submitterName: submissionData.submitterName,
          toolId: toolResult.id,
        },
      });
    }
  }

  private async sendErrorNotification(
    submissionData: ToolSubmissionJobData,
    error: any
  ): Promise<void> {
    if (!process.env.ADMIN_EMAIL) return;

    await this.jobManager.createJob(JobType.EMAIL_NOTIFICATION, {
      to: process.env.ADMIN_EMAIL,
      subject: 'Tool Submission Processing Failed - AI Dude Directory',
      template: 'admin-processing-error',
      data: {
        toolName: submissionData.name,
        toolUrl: submissionData.url,
        submitterEmail: submissionData.submitterEmail,
        error: error.message || String(error),
      },
    });
  }
}
